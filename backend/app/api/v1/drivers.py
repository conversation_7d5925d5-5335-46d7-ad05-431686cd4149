"""
Driver management endpoints for Drive On API
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Optional, List, Dict, Any
from datetime import datetime

from app.core.database import get_db
from app.core.firebase import get_current_user
from app.schemas.user import (
    DriverCreate, DriverUpdate, DriverResponse, DriverSearchParams,
    UserResponse, DriverRegistrationRequest, DriverApplicationResponse,
    PendingDriverApplication, ApplicationStatus, MaritalStatus, EducationLevel
)
from app.schemas.base import APIResponse, PaginationParams, PaginatedResponse
from app.services.firestore_service import user_service, driver_service

router = APIRouter()


@router.get("/", response_model=APIResponse)
async def get_drivers(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    city: Optional[str] = Query(None),
    state: Optional[str] = Query(None),
    min_rating: Optional[float] = Query(None, ge=0, le=5),
    max_hourly_rate: Optional[float] = Query(None, ge=0),
    min_experience: Optional[int] = Query(None, ge=0),
    is_available: Optional[bool] = Query(None),
    verified_only: bool = Query(False),
    db: Session = Depends(get_db)
):
    """
    Get list of drivers with optional filters
    """
    try:
        driver_service = DriverService(db)
        
        # Build filters
        filters = {}
        if city:
            filters["city"] = city
        if state:
            filters["state"] = state
        if min_rating is not None:
            filters["min_rating"] = min_rating
        if max_hourly_rate is not None:
            filters["max_hourly_rate"] = max_hourly_rate
        if min_experience is not None:
            filters["min_experience"] = min_experience
        if is_available is not None:
            filters["is_available"] = is_available
        if verified_only:
            filters["verified_only"] = True
        
        # Get drivers with pagination
        skip = (page - 1) * limit
        drivers = driver_service.get_drivers(skip=skip, limit=limit, filters=filters)
        
        # Convert to response format
        driver_responses = []
        for driver in drivers:
            driver_dict = {
                "id": driver.id,
                "user_id": driver.user_id,
                "license_number": driver.license_number,
                "experience_years": driver.experience_years,
                "hourly_rate": driver.hourly_rate,
                "city": driver.city,
                "state": driver.state,
                "country": driver.country,
                "is_available": driver.is_available,
                "bio": driver.bio,
                "languages": driver.languages.split(",") if driver.languages else [],
                "specialties": driver.specialties.split(",") if driver.specialties else [],
                "latitude": driver.latitude,
                "longitude": driver.longitude,
                "background_check_status": driver.background_check_status,
                "license_verified": driver.license_verified,
                "insurance_verified": driver.insurance_verified,
                "license_document_url": driver.license_document_url,
                "insurance_document_url": driver.insurance_document_url,
                "vehicle_registration_url": driver.vehicle_registration_url,
                "vehicle_make": driver.vehicle_make,
                "vehicle_model": driver.vehicle_model,
                "vehicle_year": driver.vehicle_year,
                "vehicle_color": driver.vehicle_color,
                "vehicle_plate": driver.vehicle_plate,
                "vehicle_image_url": driver.vehicle_image_url,
                "total_jobs_completed": driver.total_jobs_completed,
                "average_rating": driver.average_rating,
                "total_ratings": driver.total_ratings,
                "created_at": driver.created_at,
                "updated_at": driver.updated_at,
                "user": UserResponse.from_orm(driver.user) if hasattr(driver, 'user') else None
            }
            driver_responses.append(driver_dict)
        
        return APIResponse(
            success=True,
            message="Drivers retrieved successfully",
            data={
                "drivers": driver_responses,
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total": len(driver_responses)
                }
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get drivers: {str(e)}"
        )


@router.post("/register", response_model=APIResponse)
async def register_driver_application(
    registration_data: DriverRegistrationRequest,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Submit driver registration application with all required information and documents.
    Email is automatically fetched from Firebase authentication.
    """
    try:
        # Get current user from Firebase (email is automatically fetched from Firebase token)
        user_id = current_user["uid"]
        user_email = current_user.get("email")

        if not user_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email not found in Firebase token. Please login again."
            )

        # Check if driver application already exists
        existing_driver = driver_service.get_driver_by_user_id(user_id)
        if existing_driver:
            return APIResponse(
                success=False,
                message="Driver application already exists",
                data={
                    "application_id": existing_driver.id,
                    "status": existing_driver.application_status,
                    "message": "You have already submitted a driver application. Please wait for review."
                }
            )
        
        # Validate all required documents are provided
        required_documents = [
            registration_data.cnic_front_url,
            registration_data.cnic_back_url,
            registration_data.driving_license_url,
            registration_data.electricity_bill_url,
            registration_data.police_certificate_url
        ]
        
        if not all(required_documents):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="All required documents must be uploaded before submitting application"
            )
        
        # Create driver application with all required fields
        driver_data = {
            "user_id": user_id,
            "user_email": user_email,
            "name": registration_data.name,
            "father_name": registration_data.father_name,
            "mobile_number": registration_data.mobile_number,
            "education": registration_data.education.value,
            "experience_years": registration_data.experience_years,
            "marital_status": registration_data.marital_status.value,
            "city_of_priority": registration_data.city_of_priority,
            "profile_picture_url": registration_data.profile_picture_url,
            "cnic_front_url": registration_data.cnic_front_url,
            "cnic_back_url": registration_data.cnic_back_url,
            "driving_license_url": registration_data.driving_license_url,
            "electricity_bill_url": registration_data.electricity_bill_url,
            "police_certificate_url": registration_data.police_certificate_url,
            "bio": registration_data.bio,
            "application_status": "pending",
            "background_check_status": "pending",
            "country": "Pakistan"
        }

        # Create driver application in Firestore
        driver_id = driver_service.create_driver(driver_data)
        
        # Prepare response
        response_data = DriverApplicationResponse(
            application_id=driver_id,
            status=ApplicationStatus.PENDING,
            message="Your driver application has been submitted successfully. Our team will review your application and documents within 3-5 business days.",
            submitted_at=datetime.utcnow(),
            estimated_review_time="3-5 business days"
        )
        
        return APIResponse(
            success=True,
            message="Driver application submitted successfully",
            data=response_data.dict()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to submit driver application: {str(e)}"
        )


@router.post("/", response_model=APIResponse)
async def create_driver_profile_legacy(
    driver_data: DriverCreate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Legacy endpoint: Create driver profile for current user (backward compatibility)
    """
    try:
        user_service_sql = UserService(db)
        driver_service_sql = DriverService(db)

        # Get current user
        user = user_service_sql.get_user_by_firebase_uid(current_user["uid"])
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Check if driver profile already exists
        existing_driver = driver_service.get_driver_by_user_id(user.id)
        if existing_driver:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Driver profile already exists"
            )
        
        # Create driver profile (legacy method)
        driver = driver_service.create_driver_profile(user.id, driver_data)
        
        return APIResponse(
            success=True,
            message="Driver profile created successfully",
            data={"driver_id": driver.id}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create driver profile: {str(e)}"
        )


@router.get("/{driver_id}", response_model=APIResponse)
async def get_driver(
    driver_id: int,
    db: Session = Depends(get_db)
):
    """
    Get specific driver by ID
    """
    try:
        driver_service = DriverService(db)
        driver = driver_service.get_driver_by_id(driver_id)
        
        if not driver:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Driver not found"
            )
        
        # Convert to response format
        driver_dict = {
            "id": driver.id,
            "user_id": driver.user_id,
            "license_number": driver.license_number,
            "experience_years": driver.experience_years,
            "hourly_rate": driver.hourly_rate,
            "city": driver.city,
            "state": driver.state,
            "country": driver.country,
            "is_available": driver.is_available,
            "bio": driver.bio,
            "languages": driver.languages.split(",") if driver.languages else [],
            "specialties": driver.specialties.split(",") if driver.specialties else [],
            "background_check_status": driver.background_check_status,
            "license_verified": driver.license_verified,
            "insurance_verified": driver.insurance_verified,
            "vehicle_make": driver.vehicle_make,
            "vehicle_model": driver.vehicle_model,
            "vehicle_year": driver.vehicle_year,
            "vehicle_color": driver.vehicle_color,
            "vehicle_plate": driver.vehicle_plate,
            "total_jobs_completed": driver.total_jobs_completed,
            "average_rating": driver.average_rating,
            "total_ratings": driver.total_ratings,
            "created_at": driver.created_at,
            "updated_at": driver.updated_at
        }
        
        return APIResponse(
            success=True,
            message="Driver retrieved successfully",
            data={"driver": driver_dict}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get driver: {str(e)}"
        )


@router.put("/{driver_id}", response_model=APIResponse)
async def update_driver_profile(
    driver_id: int,
    driver_update: DriverUpdate,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update driver profile (only owner can update)
    """
    try:
        user_service = UserService(db)
        driver_service = DriverService(db)
        
        # Get current user
        user = user_service.get_user_by_firebase_uid(current_user["uid"])
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Get driver
        driver = driver_service.get_driver_by_id(driver_id)
        if not driver:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Driver not found"
            )
        
        # Check ownership
        if driver.user_id != user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to update this driver profile"
            )
        
        # Update driver profile
        update_data = driver_update.dict(exclude_unset=True)
        
        # Handle lists (convert to comma-separated strings)
        if "languages" in update_data and update_data["languages"]:
            update_data["languages"] = ",".join(update_data["languages"])
        if "specialties" in update_data and update_data["specialties"]:
            update_data["specialties"] = ",".join(update_data["specialties"])
        
        updated_driver = driver_service.update_driver_profile(driver_id, update_data)
        
        return APIResponse(
            success=True,
            message="Driver profile updated successfully",
            data={"driver_id": updated_driver.id}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update driver profile: {str(e)}"
        )


@router.get("/search", response_model=APIResponse)
async def search_drivers(
    q: str = Query(..., min_length=2),
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """
    Search drivers by name, location, or bio
    """
    try:
        driver_service = DriverService(db)
        
        skip = (page - 1) * limit
        drivers = driver_service.search_drivers(q, skip=skip, limit=limit)
        
        # Convert to response format
        driver_responses = []
        for driver in drivers:
            driver_dict = {
                "id": driver.id,
                "user_id": driver.user_id,
                "experience_years": driver.experience_years,
                "hourly_rate": driver.hourly_rate,
                "city": driver.city,
                "state": driver.state,
                "is_available": driver.is_available,
                "bio": driver.bio,
                "average_rating": driver.average_rating,
                "total_ratings": driver.total_ratings,
                "user": UserResponse.from_orm(driver.user) if hasattr(driver, 'user') else None
            }
            driver_responses.append(driver_dict)
        
        return APIResponse(
            success=True,
            message=f"Found {len(driver_responses)} drivers",
            data={
                "drivers": driver_responses,
                "search_query": q,
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total": len(driver_responses)
                }
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Search failed: {str(e)}"
        )


@router.post("/{driver_id}/verify", response_model=APIResponse)
async def verify_driver(
    driver_id: int,
    verification_type: str = Query(..., regex="^(license|insurance|background)$"),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Verify driver documents (admin only)
    """
    try:
        user_service = UserService(db)
        driver_service = DriverService(db)
        
        # Get current user
        user = user_service.get_user_by_firebase_uid(current_user["uid"])
        if not user or user.user_type != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin access required"
            )
        
        # Verify driver
        success = driver_service.verify_driver(driver_id, verification_type)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Driver not found"
            )
        
        return APIResponse(
            success=True,
            message=f"Driver {verification_type} verification completed",
            data={"driver_id": driver_id, "verification_type": verification_type}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Verification failed: {str(e)}"
        )


@router.get("/pending-applications", response_model=APIResponse)
async def get_pending_driver_applications(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    status_filter: Optional[str] = Query(None, regex="^(pending|under_review|approved|rejected)$"),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get pending driver applications (admin only)
    """
    try:
        user_service = UserService(db)
        driver_service = DriverService(db)
        
        # Get current user and verify admin access
        user = user_service.get_user_by_firebase_uid(current_user["uid"])
        if not user or user.user_type != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin access required"
            )
        
        # Get pending applications with pagination
        skip = (page - 1) * limit
        applications = driver_service.get_pending_applications(
            skip=skip, 
            limit=limit, 
            status_filter=status_filter
        )
        
        # Convert to response format
        pending_applications = []
        for driver in applications:
            app_data = PendingDriverApplication(
                id=driver.id,
                name=driver.name,
                father_name=driver.father_name,
                mobile_number=driver.mobile_number,
                education=EducationLevel(driver.education),
                experience_years=driver.experience_years,
                marital_status=MaritalStatus(driver.marital_status),
                city_of_priority=driver.city_of_priority,
                application_status=ApplicationStatus(driver.application_status),
                submitted_at=driver.created_at,
                user_email=driver.user.email if hasattr(driver, 'user') else ""
            )
            pending_applications.append(app_data.dict())
        
        return APIResponse(
            success=True,
            message="Pending applications retrieved successfully",
            data={
                "applications": pending_applications,
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total": len(pending_applications)
                }
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get pending applications: {str(e)}"
        )


@router.put("/applications/{application_id}/review", response_model=APIResponse)
async def review_driver_application(
    application_id: int,
    review_data: Dict[str, Any],
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Review and approve/reject driver application (admin only)
    """
    try:
        user_service = UserService(db)
        driver_service = DriverService(db)
        
        # Get current user and verify admin access
        user = user_service.get_user_by_firebase_uid(current_user["uid"])
        if not user or user.user_type != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin access required"
            )
        
        # Validate review data
        action = review_data.get("action")  # approve, reject, request_changes
        admin_notes = review_data.get("admin_notes", "")
        rejection_reason = review_data.get("rejection_reason", "")
        
        if action not in ["approve", "reject", "request_changes"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid action. Must be 'approve', 'reject', or 'request_changes'"
            )
        
        # Update application status
        success = driver_service.review_application(
            application_id, 
            action, 
            admin_notes, 
            rejection_reason
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Application not found"
            )
        
        return APIResponse(
            success=True,
            message=f"Application {action}d successfully",
            data={
                "application_id": application_id,
                "action": action,
                "admin_notes": admin_notes
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to review application: {str(e)}"
        )


@router.get("/my-application", response_model=APIResponse)
async def get_my_driver_application(
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get current user's driver application status
    """
    try:
        user_service = UserService(db)
        driver_service = DriverService(db)
        
        # Get current user
        user = user_service.get_user_by_firebase_uid(current_user["uid"])
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Get driver application
        driver = driver_service.get_driver_by_user_id(user.id)
        if not driver:
            return APIResponse(
                success=True,
                message="No driver application found",
                data={
                    "has_application": False,
                    "message": "You haven't submitted a driver application yet."
                }
            )
        
        return APIResponse(
            success=True,
            message="Application status retrieved successfully",
            data={
                "has_application": True,
                "application": {
                    "id": driver.id,
                    "status": driver.application_status,
                    "submitted_at": driver.created_at,
                    "admin_notes": driver.admin_notes,
                    "rejection_reason": driver.rejection_reason,
                    "approved_at": driver.approved_at
                }
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get application status: {str(e)}"
        )


@router.post("/request", response_model=APIResponse)
async def request_driver(
    request_data: Dict[str, Any],
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Request a driver for a job
    """
    try:
        # This would integrate with a matching system
        # For now, return a success response
        
        return APIResponse(
            success=True,
            message="Driver request submitted successfully",
            data={
                "request_id": "temp_id",
                "status": "pending",
                "estimated_match_time": "5-10 minutes"
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to submit driver request: {str(e)}"
        )
