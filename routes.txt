# Drive On Backend API Routes

## Base URL: http://localhost:8000

## Authentication Routes (/api/v1/auth/)
POST   /api/v1/auth/register           # Register new user with Firebase authentication
POST   /api/v1/auth/login              # Login user with Firebase token
POST   /api/v1/auth/verify-token       # Verify Firebase ID token
GET    /api/v1/auth/me                 # Get current user information
PUT    /api/v1/auth/me                 # Update current user information
POST   /api/v1/auth/logout             # Logout user (client-side token invalidation)

## Firebase Authentication Routes (/api/v1/auth-firebase/)
# Additional Firebase-specific authentication endpoints

## Driver Routes (/api/v1/drivers/)
GET    /api/v1/drivers/                # Get list of drivers with optional filters
POST   /api/v1/drivers/register        # Register as a driver (submit application)
GET    /api/v1/drivers/my-application  # Get current user's driver application status
PUT    /api/v1/drivers/my-application  # Update driver application
GET    /api/v1/drivers/applications    # Get all driver applications (admin)
PUT    /api/v1/drivers/applications/{application_id}/status  # Update application status (admin)
GET    /api/v1/drivers/{driver_id}     # Get specific driver details
PUT    /api/v1/drivers/{driver_id}     # Update driver profile
GET    /api/v1/drivers/search          # Search drivers with advanced filters
POST   /api/v1/drivers/{driver_id}/verify  # Verify driver (admin)
POST   /api/v1/drivers/request         # Request a driver for a job

## Job Routes (/api/v1/jobs/)
GET    /api/v1/jobs/                   # Get list of jobs with filters
POST   /api/v1/jobs/                   # Create new job posting
GET    /api/v1/jobs/{job_id}           # Get specific job details
PUT    /api/v1/jobs/{job_id}           # Update job posting (owner only)
DELETE /api/v1/jobs/{job_id}           # Delete job posting (owner only)
POST   /api/v1/jobs/{job_id}/contact-poster  # Contact job poster
GET    /api/v1/jobs/my                 # Get current user's job postings
GET    /api/v1/jobs/categories         # Get job categories

## Job Application Routes (/api/v1/applications/)
POST   /api/v1/applications/           # Apply to a job (drivers only)
GET    /api/v1/applications/my         # Get current user's job applications
GET    /api/v1/applications/{application_id}  # Get specific application details
PUT    /api/v1/applications/{application_id}/status  # Update application status
DELETE /api/v1/applications/{application_id}  # Withdraw application
GET    /api/v1/applications/job/{job_id}  # Get applications for a specific job (employers)
GET    /api/v1/applications/employer/all  # Get all applications for employer's jobs

## News Routes (/api/v1/news/)
GET    /api/v1/news/                   # Get news articles with advanced filtering
GET    /api/v1/news/{article_id}       # Get specific news article
GET    /api/v1/news/categories/        # Get news categories with optional statistics
GET    /api/v1/news/sources/           # Get news sources
GET    /api/v1/news/stats              # Get news statistics
POST   /api/v1/news/scrape             # Start news scraping (admin)
GET    /api/v1/news/scraping/jobs      # Get scraping jobs with status

## Forum Routes (/api/v1/forum/)
GET    /api/v1/forum/                  # Get all public forums with user membership info
GET    /api/v1/forum/{forum_id}        # Get specific forum details
POST   /api/v1/forum/{forum_id}/join   # Join a forum
POST   /api/v1/forum/{forum_id}/leave  # Leave a forum
GET    /api/v1/forum/{forum_id}/messages  # Get messages from a forum
POST   /api/v1/forum/{forum_id}/messages  # Send message to forum
POST   /api/v1/forum/messages/{message_id}/react  # Add reaction to message
DELETE /api/v1/forum/messages/{message_id}/react  # Remove reaction from message
GET    /api/v1/forum/search            # Search forum messages
GET    /api/v1/forum/cities            # Get cities for location-based forums
GET    /api/v1/forum/stats             # Get forum statistics

## File Upload Routes (/api/v1/uploads/)
POST   /api/v1/uploads/profile-image   # Upload profile image
POST   /api/v1/uploads/document        # Upload driver documents (license, insurance, etc.)
POST   /api/v1/uploads/vehicle-image   # Upload vehicle image
POST   /api/v1/uploads/voice-message   # Upload voice message for forum
POST   /api/v1/uploads/forum-voice-message  # Upload voice message specifically for forum
POST   /api/v1/uploads/forum-media     # Upload media (images, videos) for forum messages
DELETE /api/v1/uploads/file/{filename} # Delete uploaded file

## Messaging Routes (/api/v1/messaging/)
GET    /api/v1/messaging/conversations # Get all conversations for current user
POST   /api/v1/messaging/conversations # Start new conversation
GET    /api/v1/messaging/conversations/{conversation_id}/messages  # Get messages from conversation
POST   /api/v1/messaging/conversations/{conversation_id}/messages  # Send message in conversation
POST   /api/v1/messaging/contact-driver  # Job poster contacts a driver
POST   /api/v1/messaging/contact-job-poster  # Driver contacts job poster
PUT    /api/v1/messaging/conversations/{conversation_id}/mark-read  # Mark conversation as read

## Notification Routes (/api/v1/notifications/)
GET    /api/v1/notifications/          # Get user notifications
POST   /api/v1/notifications/mark-read # Mark notifications as read
POST   /api/v1/notifications/mark-all-read  # Mark all notifications as read
DELETE /api/v1/notifications/{notification_id}  # Delete specific notification

## Admin Routes (/api/v1/admin/)
GET    /api/v1/admin/dashboard         # Get admin dashboard statistics
GET    /api/v1/admin/users             # Get all users (admin only)
POST   /api/v1/admin/users/{user_id}/verify  # Verify a user account (admin only)
GET    /api/v1/admin/reports           # Get platform reports and analytics

## Firestore API Routes (/api/v1/firestore/)
GET    /api/v1/firestore/drivers/      # Get list of drivers from Firestore

## Health Check Routes
GET    /                               # Root endpoint - API status
GET    /health                         # Health check endpoint

## Response Format
All API responses follow this wrapper format:
{
  "success": true/false,
  "message": "Operation status message",
  "data": {
    // Response data here
  }
}

## Authentication
Most endpoints require Firebase authentication token in the Authorization header:
Authorization: Bearer <firebase_id_token>

## Pagination
List endpoints support pagination with query parameters:
- page: Page number (default: 1)
- limit: Items per page (default: 20, max: 100)

## Filtering
Many endpoints support filtering with query parameters:
- city: Filter by city
- state: Filter by state  
- category: Filter by category
- status: Filter by status
- search/query: Text search

## File Upload Constraints
- Profile images: Max 5MB, JPEG/PNG/GIF
- Documents: Max 10MB, PDF/JPEG/PNG
- Voice messages: Max 5 minutes, MP3/WAV/OGG/WebM
- Vehicle images: Max 10MB, JPEG/PNG/GIF

## Database Tables
- users: User accounts and basic info
- drivers: Driver profiles and details  
- jobs: Job postings
- job_applications: Job applications
- news_articles: Industry news
- ratings: User ratings and reviews

## Firebase Collections
- chat_rooms: Forum discussions
- messages: Real-time messages
- notifications: User notifications
